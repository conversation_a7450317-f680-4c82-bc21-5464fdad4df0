const Order = require("../models/Order");
const SubOrder = require("../models/SubOrder");
const Cart = require("../models/Cart");
const User = require("../models/User");
const OrderStatus = require("../constants/enums/order-status");
const PaymentMethod = require("../constants/enums/payment-method");
const Category = require("../models/Category");
const { processPayment } = require("./paymentController");
const { v4: uuidv4 } = require("uuid");

// Create a new order from the cart
exports.createOrder = async (req, res) => {
  console.log("Create Order......");
  try {
    const userId = req.user?._id;
    const {
      BillToName, email, phone, address, country,
      isCompany, companyICE, companyEmail, companyPhone, companyAddress
    } = req.body;

    // const cart = await Cart.findOne({ user: userId });
    const cart = await Cart.findOne({ user: userId }).populate("items.package");
    if (!cart || cart.items.length === 0) {
      return res.status(400).json({
        cartIsEmpty: true,
        message: req.t("order.cart_is_empty"),
      });
    }

    const shippingFee = 0;
    const tax = 0.2;
    let totalPrice = cart.totalPrice * (1 + tax);
    let taxAmount = cart.totalPrice * tax;
    let orderIdentifiant = uuidv4();

    let totalDiscount = 0;
    let subTotal = 0;
    // Create subOrders from cart items
    const subOrders = await Promise.all(
      cart.items.map(async (item) => {
        totalDiscount += item.discount;
        const basedPrice = item.price * item.quantity * item.period;
        subTotal = subTotal + basedPrice;
        const subOrderIdentifiant = uuidv4().split("-")[0];
        const subOrder = new SubOrder({
          identifiant: subOrderIdentifiant,
          package: item.package,
          quantity: item.quantity,
          basedPrice, // Price before discount
          price: basedPrice - item.discount, // Price per item
          discount: item.discount, // Discount for this sub-order
          period: item.period,
        });
        await subOrder.save();
        return subOrder._id;
      })
    );
    // totalPrice -= orderDiscount; // Apply discount to totalPrice

    // Create the order
    const newOrder = new Order({
      user: userId,
      identifiant: orderIdentifiant.split("-")[0],
      subOrders,
      paymentMethod: PaymentMethod.PAYZONE,
      status: OrderStatus.PENDING,
      taxRate: tax,
      totalPrice,
      totalDiscount,
      taxAmount,
      subTotal,
      shippingFee,
      billingInfo: {
        BillToName,
        email,
        phone,
        address,
        country,
        isCompany: isCompany || false,
        companyICE,
        companyEmail,
        companyPhone,
        companyAddress
      },
      isPaid: false,
    });

    // Save the order to the database
    const savedOrder = await newOrder.save();
    // Fetch the populated order
    const order = await Order.findById(savedOrder._id).populate("user");
    // Process initial payment record
    // COMMENTED OUT: Payment will now be processed after successful CMI callback

    const services = cart.items.map(item => ({
     serviceId: item.package,
     name: item.package?.name || 'Unknown Service',  // Ensure a default name
    period: item.period,
    price: item.price * item.quantity * item.period,
     discount: item.discount,
     quantity: item.quantity
    }));

    await processPayment({
     userId,
     orderId: order._id,
     totalPrice: order.totalPrice,
     subTotal: order.subTotal,
     totalDiscount: order.totalDiscount,
    taxAmount: order.taxAmount,
    paymentMethod: order.paymentMethod,
    status: 'completed',
    billingInfo: {
     name: BillToName,
     email,
     phone,
     address,
     country
    },
    services
  }
)
  ;


    // Clear the cart after the order is created
    // await Cart.findOneAndUpdate(
    //   { user: userId },
    //   { $set: { items: [], totalPrice: 0, totalDiscount: 0, cartCount: 0 } }
    // );

    // Send notification about new order
    if (global.io) {
      setImmediate(async () => {
        try {
          const socketService = require('../services/socketService')(global.io);

          // Populate user information for the notification
          const populatedOrder = await Order.findById(savedOrder._id).populate('user', 'firstName lastName email');

          await socketService.notifyNewOrder(populatedOrder);
          console.log(`[SOCKET DEBUG] Notification sent for new order ${savedOrder._id}`);
        } catch (error) {
          console.error('Error sending new order notification:', error);
        }
      });
    }

    res.status(201).json({
      message: req.t("order.order_creation_success"),
      order,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

exports.getMyOrders = async (req, res) => {
  try {
    const userId = req.user?._id;

    // Retrieve orders and populate the package field in subOrders
    const orders = await Order.find({ user: userId }).populate({
      path: "subOrders",
      populate: {
        path: "package",
        model: "Package",
      },
    });

    if (orders.length === 0) {
      return res.status(400).json({ message: req.t("order.no_orders") });
    }

    res.status(200).json({
      message: req.t("order.order_retrieved_successfully"),
      orders,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Get a specific order
exports.getOrder = async (req, res) => {
  try {
    const { orderId } = req.params;
    console.log("getting order: ", orderId);
    const order = await Order.findOne({
      _id: orderId,
      status: { $ne: OrderStatus.DELETED },
    })
      .populate("user")
      .populate({
        path: "subOrders",
        populate: {
          path: "package",
          model: "Package",
        },
      });

    if (!order) {
      return res.status(404).json({ message: req.t("order.order_not_found") });
    }
    res.status(200).json({
      order,
      message: req.t("order_retrieved_successfully"),
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

exports.getSubOrdersByUserIdAndCategory = async (req, res) => {
  try {
    const userId = req.user?._id;
    const { categoryName } = req.params;
    console.log("getting categoryName____: ", categoryName);

    // Find the category by name
    const category = await Category.findOne({ name: categoryName });
    if (!category) {
      return res.status(404).json({ message: "Category not found" });
    }

    // Retrieve orders by userId and populate subOrders and package
    const orders = await Order.find({
      user: userId,
      status: {
        $nin: [OrderStatus.DELETED, OrderStatus.CANCELLED, OrderStatus.PENDING]
      },
    })
      .populate({
        path: "subOrders",
        populate: {
          path: "package",
          model: "Package",
          populate: {
            path: "brand",
            model: "Brand",
            select: "category name name_fr",
            match: { category: category._id },
          },
        },
      })
      .sort({ createdAt: -1 })
      .lean(); // Use lean to improve performance

    // Filter subOrders by category
    const subOrders = orders.reduce((acc, order) => {
      const filteredSubOrders = order.subOrders.filter(
        (subOrder) => subOrder.package && subOrder.package.brand
      );
      return acc.concat(filteredSubOrders);
    }, []);

    if (subOrders.length === 0) {
      return res.status(400).json({ message: "No suborders found" });
    }

    res.status(200).json({
      message: "Suborders retrieved successfully",
      subOrders,
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Update the order status (e.g., for processing, completed, etc.)
exports.updateOrderStatus = async (req, res) => {
  const { status } = req.body;
  const { orderId } = req.params;
  try {
    // Find the order and populate user information
    const order = await Order.findById(orderId).populate('user', 'firstName lastName email');
    if (!order) {
      return res.status(404).json({ message: req.t("order_not_found") });
    }

    // Validate the new status
    const validStatuses = [
      OrderStatus.PENDING,
      OrderStatus.COMPLETED,
      OrderStatus.CANCELLED,
      OrderStatus.PROCESSING,
      OrderStatus.DELETED,
      OrderStatus.FAILED,
    ];

    if (!validStatuses.includes(status)) {
      return res.status(400).json({ message: "Invalid status" });
    }

    // Store previous status for notification
    const previousStatus = order.status;

    // Update the order status
    order.status = status;
    const savedOrder = await order.save();

    // Send notification about status update if status has changed
    if (global.io && previousStatus !== status) {
      setImmediate(async () => {
        try {
          const socketService = require('../services/socketService')(global.io);
          const user = await User.findById(req.user?._id).select('firstName lastName role');
          await socketService.notifyOrderStatusUpdate(savedOrder, previousStatus, user);
          console.log(`[SOCKET DEBUG] Notification sent for order status update ${savedOrder._id}`);
        } catch (error) {
          console.error('Error sending order status notification:', error);
        }
      });
    }

    res.status(200).json({
      order: savedOrder,
      message: "Order status updated successfully",
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Mark the order as paid
exports.markOrderAsPaid = async (req, res) => {
  try {
    const { orderId } = req.params;
    const order = await Order.findById(orderId).populate('user', 'firstName lastName email');
    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    // Store previous payment status
    const previouslyPaid = order.isPaid;

    // Update payment status
    order.isPaid = true;
    order.datePaid = Date.now();

    // Update payment record
    const payment = await processPayment({
      userId: order.user,
      orderId: order._id,
      totalPrice: order.totalPrice,
      paymentMethod: order.paymentMethod,
      status: "completed",
      transactionId: order.transactionId,
      billingInfo: order.billingInfo,
    });

    const updatedOrder = await order.save();

    // Send notification about payment confirmation if this is a new payment
    if (global.io && !previouslyPaid) {
      setImmediate(async () => {
        try {
          const socketService = require('../services/socketService')(global.io);
          await socketService.notifyPaymentConfirmation(updatedOrder, payment);
          console.log(`[SOCKET DEBUG] Payment confirmation notification sent for order ${updatedOrder._id}`);
        } catch (error) {
          console.error('Error sending payment confirmation notification:', error);
        }
      });
    }

    res.status(200).json({
      order: updatedOrder,
      message: "Order marked as paid successfully",
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Refund the order
exports.refundOrder = async (req, res) => {
  try {
    const { orderId } = req.params;
    const order = await Order.findById(orderId);
    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    order.status = OrderStatus.PROCESSINGREFUND; // Set the status to refunding
    await order.save();

    res.status(200).json({
      order,
      message: "Order refund requested successfully",
    });
  } catch (error) {
    console.error(error);
    res.status(500).json({ message: "Server error" });
  }
};

// Add this function to the exports

// Get a specific suborder by ID
exports.getSubOrderById = async (req, res) => {
  try {
    const { subOrderId } = req.params;
    const userId = req.user?._id;

    const subOrder = await SubOrder.findById(subOrderId)
      .populate({
        path: "package",
        populate: {
          path: "brand",
          model: "Brand"
        }
      });

    if (!subOrder) {
      return res.status(404).json({ message: req.t("order.order_not_found") });
    }

    // Find the parent order to verify ownership
    const parentOrder = await Order.findOne({
      subOrders: subOrderId,
      user: userId
    });

    if (!parentOrder) {
      return res.status(403).json({ message: req.t("errors.not_authorized") });
    }

    // Add the parent order reference to the subOrder
    const result = subOrder.toObject();
    result.parentOrder = parentOrder;

    res.status(200).json(result);
  } catch (error) {
    console.error("Error fetching suborder:", error);
    res.status(500).json({ message: req.t("errors.server_error") });
  }
};