"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/layout",{

/***/ "(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css":
/*!************************************************************!*\
  !*** ./node_modules/react-toastify/dist/ReactToastify.css ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"1198bfc1b7db\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC10b2FzdGlmeS9kaXN0L1JlYWN0VG9hc3RpZnkuY3NzIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtdG9hc3RpZnkvZGlzdC9SZWFjdFRvYXN0aWZ5LmNzcz83ODc0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTE5OGJmYzFiN2RiXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-toastify/dist/ReactToastify.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"1d78e88924c6\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9zdHlsZXMvZ2xvYmFscy5jc3M/OGRlOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFkNzhlODg5MjRjNlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/styles/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/config/constant.js":
/*!************************************!*\
  !*** ./src/app/config/constant.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_GOOGLE_MAP_KEY: function() { return /* binding */ API_GOOGLE_MAP_KEY; },\n/* harmony export */   BACKEND_URL: function() { return /* binding */ BACKEND_URL; },\n/* harmony export */   COOKIE_DOMAIN: function() { return /* binding */ COOKIE_DOMAIN; },\n/* harmony export */   FRONTEND_URL: function() { return /* binding */ FRONTEND_URL; },\n/* harmony export */   REACT_APP_GG_APP_ID: function() { return /* binding */ REACT_APP_GG_APP_ID; },\n/* harmony export */   isProd: function() { return /* binding */ isProd; }\n/* harmony export */ });\nconst backendDev = \"http://localhost:5002\";\nconst frontendDev = \"http://localhost:3001\";\nconst backend = \"https://api.ztechengineering.com\";\nconst frontend = \"https://ztechengineering.com\";\nconst isProd = true;\nconst BACKEND_URL = isProd ? backend : backendDev;\nconst FRONTEND_URL = isProd ? frontend : frontendDev;\nconst COOKIE_DOMAIN = isProd ? \".ztechengineering.com\" : \"localhost\";\nconst REACT_APP_GG_APP_ID = \"480987384459-h3cie2vcshp09vphuvnshccqprco3fbo.apps.googleusercontent.com\";\nconst API_GOOGLE_MAP_KEY = \"AIzaSyA5pGy3UEKwbgjUY-72RmoR7npEq1b_uf0\";\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29uZmlnL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLE1BQU1BLGFBQWE7QUFDbkIsTUFBTUMsY0FBYztBQUVwQixNQUFNQyxVQUFVO0FBQ2hCLE1BQU1DLFdBQVc7QUFFVixNQUFNQyxTQUFTLEtBQUs7QUFDcEIsTUFBTUMsY0FBY0QsU0FBU0YsVUFBVUYsV0FBVztBQUNsRCxNQUFNTSxlQUFlRixTQUFTRCxXQUFXRixZQUFZO0FBQ3JELE1BQU1NLGdCQUFnQkgsU0FBUywwQkFBMEIsWUFBWTtBQUVyRSxNQUFNSSxzQkFDWCwyRUFBMkU7QUFDdEUsTUFBTUMscUJBQXFCLDBDQUEwQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2NvbmZpZy9jb25zdGFudC5qcz9iMTZjIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhY2tlbmREZXYgPSBcImh0dHA6Ly9sb2NhbGhvc3Q6NTAwMlwiO1xyXG5jb25zdCBmcm9udGVuZERldiA9IFwiaHR0cDovL2xvY2FsaG9zdDozMDAxXCI7XHJcblxyXG5jb25zdCBiYWNrZW5kID0gXCJodHRwczovL2FwaS56dGVjaGVuZ2luZWVyaW5nLmNvbVwiO1xyXG5jb25zdCBmcm9udGVuZCA9IFwiaHR0cHM6Ly96dGVjaGVuZ2luZWVyaW5nLmNvbVwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IGlzUHJvZCA9IHRydWU7XHJcbmV4cG9ydCBjb25zdCBCQUNLRU5EX1VSTCA9IGlzUHJvZCA/IGJhY2tlbmQgOiBiYWNrZW5kRGV2O1xyXG5leHBvcnQgY29uc3QgRlJPTlRFTkRfVVJMID0gaXNQcm9kID8gZnJvbnRlbmQgOiBmcm9udGVuZERldjtcclxuZXhwb3J0IGNvbnN0IENPT0tJRV9ET01BSU4gPSBpc1Byb2QgPyBcIi56dGVjaGVuZ2luZWVyaW5nLmNvbVwiIDogXCJsb2NhbGhvc3RcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBSRUFDVF9BUFBfR0dfQVBQX0lEID1cclxuICBcIjQ4MDk4NzM4NDQ1OS1oM2NpZTJ2Y3NocDA5dnBodXZuc2hjY3FwcmNvM2Ziby5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbVwiO1xyXG5leHBvcnQgY29uc3QgQVBJX0dPT0dMRV9NQVBfS0VZID0gXCJBSXphU3lBNXBHeTNVRUt3YmdqVVktNzJSbW9SN25wRXExYl91ZjBcIjtcclxuIl0sIm5hbWVzIjpbImJhY2tlbmREZXYiLCJmcm9udGVuZERldiIsImJhY2tlbmQiLCJmcm9udGVuZCIsImlzUHJvZCIsIkJBQ0tFTkRfVVJMIiwiRlJPTlRFTkRfVVJMIiwiQ09PS0lFX0RPTUFJTiIsIlJFQUNUX0FQUF9HR19BUFBfSUQiLCJBUElfR09PR0xFX01BUF9LRVkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/config/constant.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/context/AuthContext.jsx":
/*!*****************************************!*\
  !*** ./src/app/context/AuthContext.jsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/authService */ \"(app-pages-browser)/./src/app/services/authService.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Create the Auth Context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Create a Provider Component\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cartCount, setCartCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if user exists in localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            await checkAuth();\n            setLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    const checkAuth = async ()=>{\n        setLoading(true);\n        try {\n            const response = await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].checkAuth();\n            const updatedUser = response.data.user;\n            console.log(\"Fetched user:\", updatedUser);\n            // Update only if the data is different\n            if (JSON.stringify(updatedUser) !== localStorage.getItem(\"user\")) {\n                localStorage.setItem(\"user\", JSON.stringify(updatedUser));\n                document.cookie = \"role=\".concat(updatedUser.role, \"; max-age=604800; path=/; secure\");\n            }\n            setUser(updatedUser);\n            return updatedUser;\n        } catch (err) {\n            console.error(\"Auth check failed:\", err);\n            setUser(null);\n            localStorage.removeItem(\"user\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Handle authentication error\n    const handleAuthError = (error)=>{\n        var _error_response_data, _error_response;\n        const message = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"An unexpected error occurred\";\n        console.error(error);\n        return error;\n    };\n    // Login function\n    const login = async (credentials)=>{\n        setLoading(true);\n        try {\n            const loginRes = await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].login(credentials);\n            const userData = loginRes.data.user;\n            setUser(userData);\n            // Store user in localStorage\n            localStorage.setItem(\"user\", JSON.stringify(userData));\n            document.cookie = \"role=\".concat(userData.role, \"; max-age=604800; path=/; secure\");\n            // Check for pending cart items\n            const pendingItemJson = localStorage.getItem(\"pendingCartItem\");\n            if (pendingItemJson) {\n                try {\n                    // We'll handle this in a separate function after login completes\n                    // Just mark that we have a pending item\n                    loginRes.data.hasPendingCartItem = true;\n                } catch (cartError) {\n                    console.error(\"Error handling pending cart item:\", cartError);\n                }\n            }\n            return loginRes;\n        } catch (error) {\n            var _error_response, _error_response1;\n            const detailedError = {\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                data: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data\n            };\n            throw detailedError;\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Logout function\n    const logout = async ()=>{\n        setLoading(true);\n        try {\n            await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].logout();\n            // Clear user from localStorage\n            localStorage.removeItem(\"user\");\n            // Clear cookies on logout\n            document.cookie = \"role=; Max-Age=0; path:/\";\n            document.cookie = \"refresh_token=; Max-Age=0; path=/;\";\n            document.cookie = \"token=; Max-Age=0; path=/;\";\n            setUser(null);\n            router.refresh();\n            router.push(\"/auth/login\"); // Redirect to login page after logout\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.log(\"Logout error:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Compute if user is authenticated\n    const isAuthenticated = !!user;\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            user,\n            loading,\n            login,\n            logout,\n            checkAuth,\n            cartCount,\n            setCartCount,\n            isAuthenticated\n        }), [\n        user,\n        loading,\n        cartCount,\n        checkAuth,\n        isAuthenticated\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\context\\\\AuthContext.jsx\",\n        lineNumber: 143,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AuthProvider, \"vGpTtn+k/cSLMBUJ8G2NeTQqYd0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\n// Custom hook for using AuthContext\nconst useAuth = ()=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n};\n_s1(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/context/AuthContext.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/lib/apiService.js":
/*!***********************************!*\
  !*** ./src/app/lib/apiService.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _axiosInstance__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./axiosInstance */ \"(app-pages-browser)/./src/app/lib/axiosInstance.js\");\n\nconst apiService = {\n    get: function(url) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n            params\n        });\n    },\n    post: function(url) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, data, config);\n    },\n    put: function(url) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, data, config);\n    },\n    delete: function(url) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(url, config);\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbGliL2FwaVNlcnZpY2UuanMiLCJtYXBwaW5ncyI6Ijs7QUFBNEM7QUFFNUMsTUFBTUMsYUFBYTtJQUNmQyxLQUFLLFNBQUNDO1lBQUtDLDBFQUFTLENBQUM7ZUFBTUosc0RBQWFBLENBQUNFLEdBQUcsQ0FBQ0MsS0FBSztZQUFFQztRQUFPOztJQUUzREMsTUFBTSxTQUFDRjtZQUFLRyx3RUFBTyxDQUFDLEdBQUdDLDBFQUFTLENBQUM7ZUFBTVAsc0RBQWFBLENBQUNLLElBQUksQ0FBQ0YsS0FBS0csTUFBTUM7O0lBRXJFQyxLQUFLLFNBQUNMO1lBQUtHLHdFQUFPLENBQUMsR0FBR0MsMEVBQVMsQ0FBQztlQUFNUCxzREFBYUEsQ0FBQ1EsR0FBRyxDQUFDTCxLQUFLRyxNQUFNQzs7SUFFbkVFLFFBQVEsU0FBQ047WUFBS0ksMEVBQVMsQ0FBQztlQUFNUCxzREFBYUEsQ0FBQ1MsTUFBTSxDQUFDTixLQUFLSTs7QUFDNUQ7QUFFQSwrREFBZU4sVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2xpYi9hcGlTZXJ2aWNlLmpzPzYxZDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zSW5zdGFuY2UgZnJvbSAnLi9heGlvc0luc3RhbmNlJztcclxuXHJcbmNvbnN0IGFwaVNlcnZpY2UgPSB7XHJcbiAgICBnZXQ6ICh1cmwsIHBhcmFtcyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLmdldCh1cmwsIHsgcGFyYW1zIH0pLFxyXG5cclxuICAgIHBvc3Q6ICh1cmwsIGRhdGEgPSB7fSwgY29uZmlnID0ge30pID0+IGF4aW9zSW5zdGFuY2UucG9zdCh1cmwsIGRhdGEsIGNvbmZpZyksXHJcblxyXG4gICAgcHV0OiAodXJsLCBkYXRhID0ge30sIGNvbmZpZyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLnB1dCh1cmwsIGRhdGEsIGNvbmZpZyksXHJcblxyXG4gICAgZGVsZXRlOiAodXJsLCBjb25maWcgPSB7fSkgPT4gYXhpb3NJbnN0YW5jZS5kZWxldGUodXJsLCBjb25maWcpLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgYXBpU2VydmljZTtcclxuIl0sIm5hbWVzIjpbImF4aW9zSW5zdGFuY2UiLCJhcGlTZXJ2aWNlIiwiZ2V0IiwidXJsIiwicGFyYW1zIiwicG9zdCIsImRhdGEiLCJjb25maWciLCJwdXQiLCJkZWxldGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/apiService.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!***********************************************!*\
  !*** ./src/components/home/<USER>
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Code,Edit,Info,Loader2,MessageSquare,Server,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Code,Edit,Info,Loader2,MessageSquare,Server,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Code,Edit,Info,Loader2,MessageSquare,Server,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Code,Edit,Info,Loader2,MessageSquare,Server,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Code,Edit,Info,Loader2,MessageSquare,Server,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Code,Edit,Info,Loader2,MessageSquare,Server,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Code,Edit,Info,Loader2,MessageSquare,Server,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Code,Edit,Info,Loader2,MessageSquare,Server,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Code,Edit,Info,Loader2,MessageSquare,Server,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Code,Edit,Info,Loader2,MessageSquare,Server,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Code,Edit,Info,Loader2,MessageSquare,Server,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,Check,Clock,Code,Edit,Info,Loader2,MessageSquare,Server,Shield,ShoppingBag,Star,Trash!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../app/context/AuthContext */ \"(app-pages-browser)/./src/app/context/AuthContext.jsx\");\n/* harmony import */ var _app_services_adminService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../app/services/adminService */ \"(app-pages-browser)/./src/app/services/adminService.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var _app_config_constant__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../app/config/constant */ \"(app-pages-browser)/./src/app/config/constant.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n // Assuming path\n // Assuming path\n\n\n\nconst Notifications = ()=>{\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMounted, setIsMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const notificationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user } = (0,_app_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const socket = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const timeAgo = (dateString)=>{\n        if (!dateString) return \"\";\n        try {\n            return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_7__.formatDistanceToNow)(new Date(dateString), {\n                addSuffix: true\n            });\n        } catch (e) {\n            console.error(\"Error formatting date:\", e);\n            return dateString; // fallback to original string\n        }\n    };\n    // We've removed the debug functions since they're no longer needed in production\n    // If you need to debug in the future, you can add them back\n    // State to track socket connection status\n    const [socketStatus, setSocketStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        connected: false,\n        joinedAdminRoom: false,\n        error: null\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsMounted(true);\n        console.log(\"[SOCKET DEBUG-FRONTEND] Component mounted, user:\", user ? \"available\" : \"not available\");\n        console.log(\"[SOCKET DEBUG-FRONTEND] Socket URL:\", process.env.NEXT_PUBLIC_SOCKET_URL || \"not available\");\n        // Only proceed if we have a user\n        if (user) {\n            console.log(\"[SOCKET DEBUG-FRONTEND] Initializing socket connection (automatic in all environments)\");\n            // Ensure we don't have an existing connection\n            if (socket.current) {\n                console.log(\"[SOCKET DEBUG-FRONTEND] Cleaning up existing socket connection\");\n                socket.current.disconnect();\n                socket.current = null;\n            }\n            // Use the BACKEND_URL from our constants file for socket connection\n            // This ensures we connect to the correct backend in both dev and prod environments\n            const socketUrl = _app_config_constant__WEBPACK_IMPORTED_MODULE_6__.BACKEND_URL;\n            console.log(\"[SOCKET DEBUG-FRONTEND] Using socket URL for auto-connection:\", socketUrl);\n            // Create new socket connection with explicit options\n            socket.current = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(socketUrl, {\n                query: {\n                    adminId: user._id\n                },\n                transports: [\n                    \"websocket\",\n                    \"polling\"\n                ],\n                reconnectionAttempts: 5,\n                reconnectionDelay: 1000,\n                timeout: 30000,\n                withCredentials: true,\n                forceNew: true,\n                autoConnect: true,\n                debug: !_app_config_constant__WEBPACK_IMPORTED_MODULE_6__.isProd // Enable debug mode in development\n            });\n            // Log connection attempt\n            console.log(\"[SOCKET DEBUG-FRONTEND] Attempting to auto-connect to \".concat(socketUrl, \" with transports:\"), [\n                \"websocket\",\n                \"polling\"\n            ]);\n            socket.current.on(\"connect\", ()=>{\n                console.log(\"[SOCKET DEBUG-FRONTEND] Socket connected for notifications\");\n                console.log(\"[SOCKET DEBUG-FRONTEND] Socket ID:\", socket.current.id);\n                console.log(\"[SOCKET DEBUG-FRONTEND] Connected with user ID:\", user._id);\n                // Update socket status\n                setSocketStatus((prev)=>({\n                        ...prev,\n                        connected: true,\n                        error: null\n                    }));\n                // Add a small delay before joining the admin room (ensures connection is fully established)\n                setTimeout(()=>{\n                    // Join admin room\n                    socket.current.emit(\"join-admin\");\n                    console.log(\"[SOCKET DEBUG-FRONTEND] Emitted join-admin event\");\n                    // Test notification reception\n                    socket.current.on(\"notification\", (notification)=>{\n                        console.log(\"[SOCKET DEBUG-FRONTEND] NOTIFICATION RECEIVED:\", notification);\n                        console.log(\"[SOCKET DEBUG-FRONTEND] Notification type:\", notification.type);\n                        console.log(\"[SOCKET DEBUG-FRONTEND] Notification title:\", notification.title);\n                        console.log(\"[SOCKET DEBUG-FRONTEND] Notification message:\", notification.message);\n                    });\n                }, 500);\n            });\n            // Listen for 'notification' event from the backend\n            socket.current.on(\"notification\", (newNotification)=>{\n                console.log(\"[SOCKET DEBUG-FRONTEND] New notification received via socket:\");\n                console.log(\"[SOCKET DEBUG-FRONTEND] Notification type:\", newNotification.type);\n                console.log(\"[SOCKET DEBUG-FRONTEND] Notification title:\", newNotification.title);\n                console.log(\"[SOCKET DEBUG-FRONTEND] Notification message:\", newNotification.message);\n                console.log(\"[SOCKET DEBUG-FRONTEND] Notification ID:\", newNotification._id);\n                if (true) {\n                    console.log(\"[SOCKET DEBUG-FRONTEND] Full notification data:\", newNotification);\n                }\n                // Add to the beginning of the list to show newest first\n                setNotifications((prevNotifications)=>{\n                    console.log(\"[SOCKET DEBUG-FRONTEND] Adding notification to state, current count:\", prevNotifications.length);\n                    return [\n                        newNotification,\n                        ...prevNotifications\n                    ].slice(0, 20); // Keep a reasonable limit, e.g., 20\n                });\n                if (!newNotification.isRead) {\n                    setUnreadCount((prevCount)=>{\n                        console.log(\"[SOCKET DEBUG-FRONTEND] Incrementing unread count from\", prevCount, \"to\", prevCount + 1);\n                        return prevCount + 1;\n                    });\n                }\n            });\n            // Fetch initial notifications and unread count on mount\n            const fetchInitialNotifs = async ()=>{\n                setLoading(true);\n                setError(null);\n                try {\n                    var _response_data_notifications;\n                    const response = await _app_services_adminService__WEBPACK_IMPORTED_MODULE_4__.adminService.getAdminNotifications({\n                        page: 1,\n                        limit: 20,\n                        unreadOnly: false\n                    });\n                    console.log(\"[SOCKET DEBUG-FRONTEND] Initial notifications fetched:\", ((_response_data_notifications = response.data.notifications) === null || _response_data_notifications === void 0 ? void 0 : _response_data_notifications.length) || 0);\n                    setNotifications(response.data.notifications || []);\n                    setUnreadCount(response.data.unreadCount || 0);\n                } catch (err) {\n                    console.error(\"[SOCKET DEBUG-FRONTEND] Failed to fetch initial notifications:\", err);\n                    setError(\"Failed to load notifications.\");\n                    setNotifications([]); // Clear notifications on error\n                    setUnreadCount(0);\n                } finally{\n                    setLoading(false);\n                }\n            };\n            fetchInitialNotifs();\n            socket.current.on(\"disconnect\", (reason)=>{\n                console.log(\"[SOCKET DEBUG-FRONTEND] Socket disconnected, reason:\", reason);\n                setSocketStatus((prev)=>({\n                        ...prev,\n                        connected: false,\n                        joinedAdminRoom: false\n                    }));\n            });\n            socket.current.on(\"connect_error\", (err)=>{\n                console.error(\"[SOCKET DEBUG-FRONTEND] Socket connection error:\", err.message);\n                console.error(\"[SOCKET DEBUG-FRONTEND] Socket URL that failed:\", socketUrl);\n                console.error(\"[SOCKET DEBUG-FRONTEND] Error details:\", err);\n                // Log environment information to help with debugging\n                console.log(\"[SOCKET DEBUG-FRONTEND] Environment:\", _app_config_constant__WEBPACK_IMPORTED_MODULE_6__.isProd ? \"Production\" : \"Development\");\n                console.log(\"[SOCKET DEBUG-FRONTEND] BACKEND_URL:\", _app_config_constant__WEBPACK_IMPORTED_MODULE_6__.BACKEND_URL);\n                setSocketStatus((prev)=>({\n                        ...prev,\n                        connected: false,\n                        error: err.message\n                    }));\n                // Attempt to reconnect after a delay\n                setTimeout(()=>{\n                    console.log(\"[SOCKET DEBUG-FRONTEND] Attempting to reconnect...\");\n                    if (socket.current) {\n                        socket.current.connect();\n                    }\n                }, 5000); // Try to reconnect after 5 seconds\n            });\n            socket.current.on(\"error\", (err)=>{\n                console.error(\"[SOCKET DEBUG-FRONTEND] Socket error:\", err);\n                setSocketStatus((prev)=>({\n                        ...prev,\n                        error: err.message\n                    }));\n            });\n            // Listen for admin room join confirmation\n            socket.current.on(\"admin-room-joined\", (data)=>{\n                console.log(\"[SOCKET DEBUG-FRONTEND] Received admin-room-joined confirmation:\", data);\n                if (data.success) {\n                    console.log(\"[SOCKET DEBUG-FRONTEND] Successfully joined admin room. Room size: \".concat(data.roomSize));\n                    setSocketStatus((prev)=>({\n                            ...prev,\n                            joinedAdminRoom: true\n                        }));\n                }\n            });\n            // Log reconnection attempts\n            socket.current.io.on(\"reconnect_attempt\", (attempt)=>{\n                console.log(\"[SOCKET DEBUG-FRONTEND] Socket reconnection attempt:\", attempt);\n            });\n            socket.current.io.on(\"reconnect\", (attempt)=>{\n                console.log(\"[SOCKET DEBUG-FRONTEND] Socket reconnected after\", attempt, \"attempts\");\n                setSocketStatus((prev)=>({\n                        ...prev,\n                        connected: true\n                    }));\n            });\n        } else {\n            console.log(\"[SOCKET DEBUG-FRONTEND] User not available, skipping socket connection\");\n        }\n        return ()=>{\n            if (socket.current) {\n                console.log(\"[SOCKET DEBUG-FRONTEND] Cleaning up socket on component unmount\");\n                socket.current.disconnect();\n            }\n        };\n    }, [\n        user\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isMounted) return;\n        const handleClickOutside = (event)=>{\n            if (notificationRef.current && !notificationRef.current.contains(event.target)) {\n                setIsOpen(false);\n            }\n        };\n        if (isOpen) {\n            document.addEventListener(\"mousedown\", handleClickOutside);\n        }\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, [\n        isOpen,\n        isMounted\n    ]);\n    const toggleNotifications = ()=>{\n        setIsOpen(!isOpen);\n    };\n    const handleMarkAsRead = async (notificationId)=>{\n        try {\n            await _app_services_adminService__WEBPACK_IMPORTED_MODULE_4__.adminService.markNotificationAsRead(notificationId);\n            setNotifications((prev)=>prev.map((n)=>n._id === notificationId ? {\n                        ...n,\n                        isRead: true\n                    } : n));\n            setUnreadCount((prev)=>Math.max(0, prev - 1));\n        } catch (err) {\n            console.error(\"Failed to mark notification as read:\", err);\n        // Optionally show a toast error\n        }\n    };\n    const handleMarkAllAsRead = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _app_services_adminService__WEBPACK_IMPORTED_MODULE_4__.adminService.markAllNotificationsAsRead();\n            // Update local state to mark all notifications as read\n            setNotifications((prev)=>prev.map((n)=>({\n                        ...n,\n                        isRead: true\n                    })));\n            setUnreadCount(0);\n            console.log(\"[NOTIFICATIONS] Successfully marked all notifications as read:\", response);\n        } catch (err) {\n            console.error(\"[NOTIFICATIONS] Failed to mark all notifications as read:\", err);\n        // Optionally show a toast error\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Function to manually reconnect the socket\n    const handleReconnect = ()=>{\n        if (!socket.current) {\n            return;\n        }\n        console.log(\"[SOCKET DEBUG-FRONTEND] Manual reconnection attempt\");\n        // First disconnect if already connected\n        if (socket.current.connected) {\n            socket.current.disconnect();\n        }\n        // Then try to reconnect\n        socket.current.connect();\n        // Update UI to show connecting state\n        setSocketStatus((prev)=>({\n                ...prev,\n                connected: false,\n                error: null\n            }));\n    };\n    // We've removed the debug functions since they're no longer needed in production\n    // If you need to debug in the future, you can add them back\n    const handleNotificationClick = (notification)=>{\n        if (!notification.isRead) {\n            handleMarkAsRead(notification._id);\n        }\n        console.log(\"\\uD83D\\uDD14 [ADMIN] Notification clicked!\");\n        console.log(\"\\uD83D\\uDCCB [ADMIN] Notification type:\", notification.type);\n        console.log(\"\\uD83D\\uDD17 [ADMIN] Notification link:\", notification.link);\n        console.log(\"\\uD83D\\uDCC4 [ADMIN] Full notification object:\", notification);\n        // Handle order-related notifications (order_update, ssl_update, hosting_update, webdev_update)\n        if (notification.type === \"order_update\" || notification.type === \"ssl_update\" || notification.type === \"hosting_update\" || notification.type === \"webdev_update\") {\n            // Check if notification has a link property (preferred way)\n            if (notification.link) {\n                console.log(\"✅ [ADMIN] Using notification link:\", notification.link);\n                router.push(notification.link);\n            } else {\n                console.log(\"⚠️ [ADMIN] No link found, trying to extract order ID from message\");\n                const orderIdMatch = notification.message.match(/Order #([\\w\\d]+)/i);\n                if (orderIdMatch && orderIdMatch[1]) {\n                    const orderId = orderIdMatch[1];\n                    const fallbackUrl = \"/admin/orders/\".concat(orderId);\n                    console.log(\"✅ [ADMIN] Using extracted order ID:\", fallbackUrl);\n                    router.push(fallbackUrl);\n                } else {\n                    console.warn(\"❌ [ADMIN] Could not extract order ID from notification:\", notification);\n                    // Navigate to orders list as fallback\n                    router.push(\"/admin/orders\");\n                }\n            }\n        } else if (notification.type === \"new_ticket\" || notification.type === \"ticket_updated\" || notification.type === \"ticket_status_update\") {\n            // If notification has a specific ticket ID, navigate to the ticket section\n            router.push(\"/admin/support\");\n        } else if (notification.type === \"user_registered\" || notification.type === \"user_verified\") {\n            if (notification.link) {\n                router.push(notification.link);\n            } else {\n                router.push(\"/admin/users\");\n            }\n        } else if (notification.link) {\n            console.log(\"\\uD83D\\uDD17 [ADMIN] Using generic link fallback:\", notification.link);\n            router.push(notification.link);\n        } else {\n            console.log(\"ℹ️ [ADMIN] No specific handler for notification type:\", notification.type);\n        }\n        setIsOpen(false); // Close dropdown after handling the notification\n    };\n    if (!isMounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"relative p-2 rounded-full hover:bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-5 h-5 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 358,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 357,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n            lineNumber: 356,\n            columnNumber: 7\n        }, undefined);\n    }\n    const getNotificationIcon = (type)=>{\n        // Assuming notification types like 'new_ticket', 'ticket_status_update', 'order_update', etc.\n        // You might want to map these types to specific icons and colors.\n        switch(type){\n            case \"new_ticket\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 369,\n                    columnNumber: 16\n                }, undefined);\n            case \"ticket_status_update\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 371,\n                    columnNumber: 16\n                }, undefined);\n            case \"ticket_updated\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"w-4 h-4 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 373,\n                    columnNumber: 16\n                }, undefined);\n            case \"ticket_deleted\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 375,\n                    columnNumber: 16\n                }, undefined);\n            case \"order_update\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"w-4 h-4 text-amber-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 377,\n                    columnNumber: 16\n                }, undefined);\n            case \"ssl_expiry\":\n            case \"ssl_update\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 380,\n                    columnNumber: 16\n                }, undefined);\n            case \"hosting_update\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 382,\n                    columnNumber: 16\n                }, undefined);\n            case \"webdev_update\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                    className: \"w-4 h-4 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 384,\n                    columnNumber: 16\n                }, undefined);\n            case \"abandoned_cart\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"w-4 h-4 text-orange-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 386,\n                    columnNumber: 16\n                }, undefined);\n            case \"custom_notification\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                    className: \"w-4 h-4 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 388,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 390,\n                    columnNumber: 16\n                }, undefined);\n        }\n    };\n    // Function to get enhanced notification message with context\n    const getEnhancedMessage = (notification)=>{\n        // For order notifications, add more context\n        if (notification.type === \"order_update\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-600\",\n                        children: notification.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 400,\n                        columnNumber: 11\n                    }, undefined),\n                    notification.actionBy && notification.actionBy.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: [\n                            \"By: \",\n                            notification.actionBy.firstName,\n                            \" \",\n                            notification.actionBy.lastName,\n                            notification.actionBy.role && \" (\".concat(notification.actionBy.role, \")\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 402,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 399,\n                columnNumber: 9\n            }, undefined);\n        }\n        // For SSL notifications, add more context\n        if (notification.type === \"ssl_expiry\" || notification.type === \"ssl_update\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-600\",\n                        children: notification.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, undefined),\n                    notification.actionBy && notification.actionBy.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: [\n                            \"By: \",\n                            notification.actionBy.firstName,\n                            \" \",\n                            notification.actionBy.lastName,\n                            notification.actionBy.role && \" (\".concat(notification.actionBy.role, \")\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 417,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 414,\n                columnNumber: 9\n            }, undefined);\n        }\n        // For hosting notifications, add more context\n        if (notification.type === \"hosting_update\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-600\",\n                        children: notification.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 430,\n                        columnNumber: 11\n                    }, undefined),\n                    notification.actionBy && notification.actionBy.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: [\n                            \"By: \",\n                            notification.actionBy.firstName,\n                            \" \",\n                            notification.actionBy.lastName,\n                            notification.actionBy.role && \" (\".concat(notification.actionBy.role, \")\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 432,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 429,\n                columnNumber: 9\n            }, undefined);\n        }\n        // For web development notifications, add more context\n        if (notification.type === \"webdev_update\") {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-600\",\n                        children: notification.message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 445,\n                        columnNumber: 11\n                    }, undefined),\n                    notification.actionBy && notification.actionBy.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-gray-500 mt-1\",\n                        children: [\n                            \"By: \",\n                            notification.actionBy.firstName,\n                            \" \",\n                            notification.actionBy.lastName,\n                            notification.actionBy.role && \" (\".concat(notification.actionBy.role, \")\")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 447,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 444,\n                columnNumber: 9\n            }, undefined);\n        }\n        // Default display for other notification types\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-600\",\n                    children: notification.message\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 459,\n                    columnNumber: 9\n                }, undefined),\n                notification.actionBy && notification.actionBy.firstName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-xs text-gray-500 mt-1\",\n                    children: [\n                        \"By: \",\n                        notification.actionBy.firstName,\n                        \" \",\n                        notification.actionBy.lastName,\n                        notification.actionBy.role && \" (\".concat(notification.actionBy.role, \")\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                    lineNumber: 461,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n            lineNumber: 458,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: notificationRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"relative p-2 rounded-full hover:bg-gray-100 border border-gray-100\",\n                \"aria-label\": \"Notifications\",\n                onClick: toggleNotifications,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        className: \"w-5 h-5 text-gray-600 \"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 477,\n                        columnNumber: 9\n                    }, undefined),\n                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"absolute top-0 right-0 flex size-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"absolute inline-flex h-full w-full animate-ping rounded-full bg-red-400 opacity-75\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 480,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"relative inline-flex size-2 rounded-full bg-red-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 481,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 479,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 472,\n                columnNumber: 7\n            }, undefined),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-custom-heavy border border-gray-200 z-50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-b border-gray-200 bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-gray-700\",\n                                        children: \"Notifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-0.5 bg-primary/10 text-primary text-xs font-medium rounded-full\",\n                                        children: [\n                                            unreadCount,\n                                            \" new\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 490,\n                                columnNumber: 13\n                            }, undefined),\n                            socketStatus.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end mt-2 text-xs\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        handleReconnect();\n                                    },\n                                    className: \"px-2 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors\",\n                                    children: \"Reconnect\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 501,\n                                columnNumber: 15\n                            }, undefined),\n                            !_app_config_constant__WEBPACK_IMPORTED_MODULE_6__.isProd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 pt-2 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Socket:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        socketStatus.connected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-600 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-500 rounded-full mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                \"Connected\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                            lineNumber: 520,\n                                            columnNumber: 21\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-600 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-red-500 rounded-full mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                \"Disconnected\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        socketStatus.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-500 ml-2\",\n                                            children: [\n                                                \"Error: \",\n                                                socketStatus.error\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 516,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 489,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-h-96 overflow-y-auto hide-scrollbar\",\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center items-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"w-6 h-6 animate-spin text-primary\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                lineNumber: 541,\n                                columnNumber: 17\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 540,\n                            columnNumber: 15\n                        }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-8 px-4 text-center text-red-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"w-8 h-8 mx-auto mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 545,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 544,\n                            columnNumber: 15\n                        }, undefined) : notifications.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"divide-y divide-gray-100\",\n                            children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 hover:bg-gray-50 transition-colors cursor-pointer \".concat(!notification.isRead ? \"bg-blue-50/50\" : \"\"),\n                                    onClick: ()=>handleNotificationClick(notification),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 mt-0.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-8 h-8 rounded-full flex items-center justify-center \".concat(!notification.isRead ? \"bg-primary/10\" : \"bg-gray-100\"),\n                                                    children: getNotificationIcon(notification.type)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                lineNumber: 559,\n                                                columnNumber: 23\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between items-start mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium truncate \".concat(!notification.isRead ? \"text-primary\" : \"text-gray-800\"),\n                                                                children: notification.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                                lineNumber: 569,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-gray-500 whitespace-nowrap ml-2\",\n                                                                children: timeAgo(notification.createdAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                        lineNumber: 568,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    getEnhancedMessage(notification)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                        lineNumber: 558,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, notification._id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 19\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 549,\n                            columnNumber: 15\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-8 px-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_Check_Clock_Code_Edit_Info_Loader2_MessageSquare_Server_Shield_ShoppingBag_Star_Trash_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 mb-1\",\n                                    children: \"No notifications yet\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-400\",\n                                    children: [\n                                        \"We\",\n                                        \"'\",\n                                        \"ll notify you when something arrives\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 585,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 538,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 border-t border-gray-200 bg-gray-50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleMarkAllAsRead,\n                                    disabled: unreadCount === 0 || loading,\n                                    className: \"text-sm text-primary hover:text-blue-700 font-medium transition-colors py-1 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Mark all as read\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        router.push(\"/admin/notifications\");\n                                        setIsOpen(false);\n                                    },\n                                    className: \"text-sm text-gray-500 hover:text-gray-700 transition-colors py-1\",\n                                    children: \"View all\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                            lineNumber: 596,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                        lineNumber: 595,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n                lineNumber: 488,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\Notifications.jsx\",\n        lineNumber: 471,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Notifications, \"Hh26TcFbdNve32HsdRE4wKtdHqM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _app_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = Notifications;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Notifications);\nvar _c;\n$RefreshReg$(_c, \"Notifications\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/code.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Code; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"polyline\",\n        {\n            points: \"16 18 22 12 16 6\",\n            key: \"z7tu5w\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"8 6 2 12 8 18\",\n            key: \"1eg1df\"\n        }\n    ]\n];\nconst Code = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Code\", __iconNode);\n //# sourceMappingURL=code.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY29kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVxRDtBQUV0RCxNQUFNQyxhQUFhO0lBQ2pCO1FBQUM7UUFBWTtZQUFFQyxRQUFRO1lBQW9CQyxLQUFLO1FBQVM7S0FBRTtJQUMzRDtRQUFDO1FBQVk7WUFBRUQsUUFBUTtZQUFpQkMsS0FBSztRQUFTO0tBQUU7Q0FDekQ7QUFDRCxNQUFNQyxPQUFPSixnRUFBZ0JBLENBQUMsUUFBUUM7QUFFQyxDQUN2QyxnQ0FBZ0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9jb2RlLmpzPzZjYTMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuNDc1LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBfX2ljb25Ob2RlID0gW1xuICBbXCJwb2x5bGluZVwiLCB7IHBvaW50czogXCIxNiAxOCAyMiAxMiAxNiA2XCIsIGtleTogXCJ6N3R1NXdcIiB9XSxcbiAgW1wicG9seWxpbmVcIiwgeyBwb2ludHM6IFwiOCA2IDIgMTIgOCAxOFwiLCBrZXk6IFwiMWVnMWRmXCIgfV1cbl07XG5jb25zdCBDb2RlID0gY3JlYXRlTHVjaWRlSWNvbihcIkNvZGVcIiwgX19pY29uTm9kZSk7XG5cbmV4cG9ydCB7IF9faWNvbk5vZGUsIENvZGUgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29kZS5qcy5tYXBcbiJdLCJuYW1lcyI6WyJjcmVhdGVMdWNpZGVJY29uIiwiX19pY29uTm9kZSIsInBvaW50cyIsImtleSIsIkNvZGUiLCJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/server.js ***!
  \************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Server; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"8\",\n            x: \"2\",\n            y: \"2\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"ngkwjq\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"8\",\n            x: \"2\",\n            y: \"14\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"iecqi9\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"6\",\n            x2: \"6.01\",\n            y1: \"6\",\n            y2: \"6\",\n            key: \"16zg32\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"6\",\n            x2: \"6.01\",\n            y1: \"18\",\n            y2: \"18\",\n            key: \"nzw8ys\"\n        }\n    ]\n];\nconst Server = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Server\", __iconNode);\n //# sourceMappingURL=server.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\n"));

/***/ })

});