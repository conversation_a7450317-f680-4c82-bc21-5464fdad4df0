"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(sharedPages)/hosting/vps/page",{

/***/ "(app-pages-browser)/./node_modules/react-loading-skeleton/dist/skeleton.css":
/*!***************************************************************!*\
  !*** ./node_modules/react-loading-skeleton/dist/skeleton.css ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"f3bf8b5c5159\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1sb2FkaW5nLXNrZWxldG9uL2Rpc3Qvc2tlbGV0b24uY3NzIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtbG9hZGluZy1za2VsZXRvbi9kaXN0L3NrZWxldG9uLmNzcz9lOWEwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiZjNiZjhiNWM1MTU5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-loading-skeleton/dist/skeleton.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/vps/page.jsx":
/*!*************************************************************!*\
  !*** ./src/app/[locale]/(sharedPages)/hosting/vps/page.jsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VPSHostingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_loading_skeleton_dist_skeleton_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-loading-skeleton/dist/skeleton.css */ \"(app-pages-browser)/./node_modules/react-loading-skeleton/dist/skeleton.css\");\n/* harmony import */ var _components_hosting_pricingPlanGrid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/hosting/pricingPlanGrid */ \"(app-pages-browser)/./src/components/hosting/pricingPlanGrid.jsx\");\n/* harmony import */ var _app_helpers_helpers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/helpers */ \"(app-pages-browser)/./src/app/helpers/helpers.js\");\n/* harmony import */ var _app_services_packageService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/services/packageService */ \"(app-pages-browser)/./src/app/services/packageService.js\");\n/* harmony import */ var lottie_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lottie-react */ \"(app-pages-browser)/./node_modules/lottie-react/build/index.umd.js\");\n/* harmony import */ var lottie_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lottie_react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var src_assets_vps_hosting_json__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! src/assets/vps-hosting.json */ \"(app-pages-browser)/./src/assets/vps-hosting.json\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_RocketIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,RocketIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_RocketIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,RocketIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _components_DynamicMetadataClient__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/DynamicMetadataClient */ \"(app-pages-browser)/./src/components/DynamicMetadataClient.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction VPSHostingPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_10__.useTranslations)(\"hosting\");\n    const [billingPeriod, setBillingPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"yearly\");\n    const [dedicatedHostingPacks, setDedicatedHostingPacks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getPackagesData = async ()=>{\n            try {\n                const response = await _app_services_packageService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPackages(\"VPS Hosting\");\n                setDedicatedHostingPacks(response.data);\n            } catch (error) {\n                console.error(\"error fetching promotions: \", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        getPackagesData();\n    }, []);\n    const dedicatedPlans = (0,_app_helpers_helpers__WEBPACK_IMPORTED_MODULE_5__.getFormattedPlans)(dedicatedHostingPacks, billingPeriod);\n    const maxDiscount = (0,_app_helpers_helpers__WEBPACK_IMPORTED_MODULE_5__.getMaxDiscount)(dedicatedHostingPacks);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 pt-4 pb-20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DynamicMetadataClient__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                title: \"ZtechEngineering | \".concat(t(\"vps.title\")),\n                desc: \"\".concat(t(\"vps.desc\"))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center lg:text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"h1\",\n                                            color: \"blue-gray\",\n                                            className: \"text-4xl lg:text-5xl font-inter font-bold mb-6 tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-[#0B2D6A] to-indigo-500\",\n                                            children: t(\"vps_headline\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"lead\",\n                                            color: \"gray\",\n                                            className: \"text-lg mb-8 font-light\",\n                                            children: t(\"vps_description\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            color: \"blue\",\n                                            size: \"lg\",\n                                            className: \"md:hidden rounded-full flex gap-2 justify-center items-center w-full sm:w-auto\",\n                                            onClick: ()=>{\n                                                var _document_getElementById;\n                                                return (_document_getElementById = document.getElementById(\"pricing\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                    behavior: \"smooth\"\n                                                });\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_RocketIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-6 h-6 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, this),\n                                                t(\"explore_plans\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center lg:justify-end\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((lottie_react__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                        animationData: src_assets_vps_hosting_json__WEBPACK_IMPORTED_MODULE_8__,\n                                        loop: true,\n                                        className: \"w-80 max-w-sm lg:max-w-md\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        id: \"pricing\",\n                        className: \"mt-[-30px] mb-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hosting_pricingPlanGrid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            loading: loading,\n                            plans: dedicatedPlans,\n                            billingPeriod: billingPeriod,\n                            t: t,\n                            setBillingPeriod: setBillingPeriod,\n                            getMaxDiscount: ()=>maxDiscount\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                        className: \"py-16 bg-gray-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"h2\",\n                                        color: \"blue-gray\",\n                                        className: \"text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-4\",\n                                        children: t(\"features_title\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"lead\",\n                                        color: \"gray\",\n                                        className: \"text-lg md:text-xl font-light text-gray-600 max-w-2xl mx-auto\",\n                                        children: t(\"features_description\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 px-4 sm:px-6 lg:px-8\",\n                                children: [\n                                    {\n                                        title: t(\"vps_features.0.title\"),\n                                        description: t(\"vps_features.0.description\"),\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_RocketIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 23\n                                        }, this)\n                                    },\n                                    {\n                                        title: t(\"vps_features.1.title\"),\n                                        description: t(\"vps_features.1.description\"),\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_RocketIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 23\n                                        }, this)\n                                    },\n                                    {\n                                        title: t(\"vps_features.2.title\"),\n                                        description: t(\"vps_features.2.description\"),\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_RocketIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-700\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 23\n                                        }, this)\n                                    }\n                                ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                        className: \"bg-white hover:shadow-2xl transition-shadow duration-300 rounded-2xl border border-gray-100 p-6 flex flex-col items-center text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-6\",\n                                                children: feature.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                                                className: \"p-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                        variant: \"h5\",\n                                                        color: \"blue-gray\",\n                                                        className: \"text-xl md:text-2xl font-semibold mb-3\",\n                                                        children: feature.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                        color: \"gray\",\n                                                        className: \"text-sm md:text-base font-normal text-gray-600\",\n                                                        children: feature.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\vps\\\\page.jsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_s(VPSHostingPage, \"j/xASROy8krvzTQtbRJvI4uzyK0=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_10__.useTranslations\n    ];\n});\n_c = VPSHostingPage;\nvar _c;\n$RefreshReg$(_c, \"VPSHostingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/vps/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/config/constant.js":
/*!************************************!*\
  !*** ./src/app/config/constant.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_GOOGLE_MAP_KEY: function() { return /* binding */ API_GOOGLE_MAP_KEY; },\n/* harmony export */   BACKEND_URL: function() { return /* binding */ BACKEND_URL; },\n/* harmony export */   COOKIE_DOMAIN: function() { return /* binding */ COOKIE_DOMAIN; },\n/* harmony export */   FRONTEND_URL: function() { return /* binding */ FRONTEND_URL; },\n/* harmony export */   REACT_APP_GG_APP_ID: function() { return /* binding */ REACT_APP_GG_APP_ID; },\n/* harmony export */   isProd: function() { return /* binding */ isProd; }\n/* harmony export */ });\nconst backendDev = \"http://localhost:5002\";\nconst frontendDev = \"http://localhost:3001\";\nconst backend = \"https://api.ztechengineering.com\";\nconst frontend = \"https://ztechengineering.com\";\nconst isProd = true;\nconst BACKEND_URL = isProd ? backend : backendDev;\nconst FRONTEND_URL = isProd ? frontend : frontendDev;\nconst COOKIE_DOMAIN = isProd ? \".ztechengineering.com\" : \"localhost\";\nconst REACT_APP_GG_APP_ID = \"480987384459-h3cie2vcshp09vphuvnshccqprco3fbo.apps.googleusercontent.com\";\nconst API_GOOGLE_MAP_KEY = \"AIzaSyA5pGy3UEKwbgjUY-72RmoR7npEq1b_uf0\";\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29uZmlnL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLE1BQU1BLGFBQWE7QUFDbkIsTUFBTUMsY0FBYztBQUVwQixNQUFNQyxVQUFVO0FBQ2hCLE1BQU1DLFdBQVc7QUFFVixNQUFNQyxTQUFTLEtBQUs7QUFDcEIsTUFBTUMsY0FBY0QsU0FBU0YsVUFBVUYsV0FBVztBQUNsRCxNQUFNTSxlQUFlRixTQUFTRCxXQUFXRixZQUFZO0FBQ3JELE1BQU1NLGdCQUFnQkgsU0FBUywwQkFBMEIsWUFBWTtBQUVyRSxNQUFNSSxzQkFDWCwyRUFBMkU7QUFDdEUsTUFBTUMscUJBQXFCLDBDQUEwQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2NvbmZpZy9jb25zdGFudC5qcz9iMTZjIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhY2tlbmREZXYgPSBcImh0dHA6Ly9sb2NhbGhvc3Q6NTAwMlwiO1xyXG5jb25zdCBmcm9udGVuZERldiA9IFwiaHR0cDovL2xvY2FsaG9zdDozMDAxXCI7XHJcblxyXG5jb25zdCBiYWNrZW5kID0gXCJodHRwczovL2FwaS56dGVjaGVuZ2luZWVyaW5nLmNvbVwiO1xyXG5jb25zdCBmcm9udGVuZCA9IFwiaHR0cHM6Ly96dGVjaGVuZ2luZWVyaW5nLmNvbVwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IGlzUHJvZCA9IHRydWU7XHJcbmV4cG9ydCBjb25zdCBCQUNLRU5EX1VSTCA9IGlzUHJvZCA/IGJhY2tlbmQgOiBiYWNrZW5kRGV2O1xyXG5leHBvcnQgY29uc3QgRlJPTlRFTkRfVVJMID0gaXNQcm9kID8gZnJvbnRlbmQgOiBmcm9udGVuZERldjtcclxuZXhwb3J0IGNvbnN0IENPT0tJRV9ET01BSU4gPSBpc1Byb2QgPyBcIi56dGVjaGVuZ2luZWVyaW5nLmNvbVwiIDogXCJsb2NhbGhvc3RcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBSRUFDVF9BUFBfR0dfQVBQX0lEID1cclxuICBcIjQ4MDk4NzM4NDQ1OS1oM2NpZTJ2Y3NocDA5dnBodXZuc2hjY3FwcmNvM2Ziby5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbVwiO1xyXG5leHBvcnQgY29uc3QgQVBJX0dPT0dMRV9NQVBfS0VZID0gXCJBSXphU3lBNXBHeTNVRUt3YmdqVVktNzJSbW9SN25wRXExYl91ZjBcIjtcclxuIl0sIm5hbWVzIjpbImJhY2tlbmREZXYiLCJmcm9udGVuZERldiIsImJhY2tlbmQiLCJmcm9udGVuZCIsImlzUHJvZCIsIkJBQ0tFTkRfVVJMIiwiRlJPTlRFTkRfVVJMIiwiQ09PS0lFX0RPTUFJTiIsIlJFQUNUX0FQUF9HR19BUFBfSUQiLCJBUElfR09PR0xFX01BUF9LRVkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/config/constant.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/context/AuthContext.jsx":
/*!*****************************************!*\
  !*** ./src/app/context/AuthContext.jsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/authService */ \"(app-pages-browser)/./src/app/services/authService.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Create the Auth Context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Create a Provider Component\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cartCount, setCartCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if user exists in localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            await checkAuth();\n            setLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    const checkAuth = async ()=>{\n        setLoading(true);\n        try {\n            const response = await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].checkAuth();\n            const updatedUser = response.data.user;\n            console.log(\"Fetched user:\", updatedUser);\n            // Update only if the data is different\n            if (JSON.stringify(updatedUser) !== localStorage.getItem(\"user\")) {\n                localStorage.setItem(\"user\", JSON.stringify(updatedUser));\n                document.cookie = \"role=\".concat(updatedUser.role, \"; max-age=604800; path=/; secure\");\n            }\n            setUser(updatedUser);\n            return updatedUser;\n        } catch (err) {\n            console.error(\"Auth check failed:\", err);\n            setUser(null);\n            localStorage.removeItem(\"user\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Handle authentication error\n    const handleAuthError = (error)=>{\n        var _error_response_data, _error_response;\n        const message = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"An unexpected error occurred\";\n        console.error(error);\n        return error;\n    };\n    // Login function\n    const login = async (credentials)=>{\n        setLoading(true);\n        try {\n            const loginRes = await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].login(credentials);\n            const userData = loginRes.data.user;\n            setUser(userData);\n            // Store user in localStorage\n            localStorage.setItem(\"user\", JSON.stringify(userData));\n            document.cookie = \"role=\".concat(userData.role, \"; max-age=604800; path=/; secure\");\n            // Check for pending cart items\n            const pendingItemJson = localStorage.getItem(\"pendingCartItem\");\n            if (pendingItemJson) {\n                try {\n                    // We'll handle this in a separate function after login completes\n                    // Just mark that we have a pending item\n                    loginRes.data.hasPendingCartItem = true;\n                } catch (cartError) {\n                    console.error(\"Error handling pending cart item:\", cartError);\n                }\n            }\n            return loginRes;\n        } catch (error) {\n            var _error_response, _error_response1;\n            const detailedError = {\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                data: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data\n            };\n            throw detailedError;\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Logout function\n    const logout = async ()=>{\n        setLoading(true);\n        try {\n            await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].logout();\n            // Clear user from localStorage\n            localStorage.removeItem(\"user\");\n            // Clear cookies on logout\n            document.cookie = \"role=; Max-Age=0; path:/\";\n            document.cookie = \"refresh_token=; Max-Age=0; path=/;\";\n            document.cookie = \"token=; Max-Age=0; path=/;\";\n            setUser(null);\n            router.refresh();\n            router.push(\"/auth/login\"); // Redirect to login page after logout\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.log(\"Logout error:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Compute if user is authenticated\n    const isAuthenticated = !!user;\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            user,\n            loading,\n            login,\n            logout,\n            checkAuth,\n            cartCount,\n            setCartCount,\n            isAuthenticated\n        }), [\n        user,\n        loading,\n        cartCount,\n        checkAuth,\n        isAuthenticated\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\context\\\\AuthContext.jsx\",\n        lineNumber: 143,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AuthProvider, \"vGpTtn+k/cSLMBUJ8G2NeTQqYd0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\n// Custom hook for using AuthContext\nconst useAuth = ()=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n};\n_s1(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/context/AuthContext.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/lib/apiService.js":
/*!***********************************!*\
  !*** ./src/app/lib/apiService.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _axiosInstance__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./axiosInstance */ \"(app-pages-browser)/./src/app/lib/axiosInstance.js\");\n\nconst apiService = {\n    get: function(url) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n            params\n        });\n    },\n    post: function(url) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, data, config);\n    },\n    put: function(url) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, data, config);\n    },\n    delete: function(url) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(url, config);\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbGliL2FwaVNlcnZpY2UuanMiLCJtYXBwaW5ncyI6Ijs7QUFBNEM7QUFFNUMsTUFBTUMsYUFBYTtJQUNmQyxLQUFLLFNBQUNDO1lBQUtDLDBFQUFTLENBQUM7ZUFBTUosc0RBQWFBLENBQUNFLEdBQUcsQ0FBQ0MsS0FBSztZQUFFQztRQUFPOztJQUUzREMsTUFBTSxTQUFDRjtZQUFLRyx3RUFBTyxDQUFDLEdBQUdDLDBFQUFTLENBQUM7ZUFBTVAsc0RBQWFBLENBQUNLLElBQUksQ0FBQ0YsS0FBS0csTUFBTUM7O0lBRXJFQyxLQUFLLFNBQUNMO1lBQUtHLHdFQUFPLENBQUMsR0FBR0MsMEVBQVMsQ0FBQztlQUFNUCxzREFBYUEsQ0FBQ1EsR0FBRyxDQUFDTCxLQUFLRyxNQUFNQzs7SUFFbkVFLFFBQVEsU0FBQ047WUFBS0ksMEVBQVMsQ0FBQztlQUFNUCxzREFBYUEsQ0FBQ1MsTUFBTSxDQUFDTixLQUFLSTs7QUFDNUQ7QUFFQSwrREFBZU4sVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2xpYi9hcGlTZXJ2aWNlLmpzPzYxZDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zSW5zdGFuY2UgZnJvbSAnLi9heGlvc0luc3RhbmNlJztcclxuXHJcbmNvbnN0IGFwaVNlcnZpY2UgPSB7XHJcbiAgICBnZXQ6ICh1cmwsIHBhcmFtcyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLmdldCh1cmwsIHsgcGFyYW1zIH0pLFxyXG5cclxuICAgIHBvc3Q6ICh1cmwsIGRhdGEgPSB7fSwgY29uZmlnID0ge30pID0+IGF4aW9zSW5zdGFuY2UucG9zdCh1cmwsIGRhdGEsIGNvbmZpZyksXHJcblxyXG4gICAgcHV0OiAodXJsLCBkYXRhID0ge30sIGNvbmZpZyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLnB1dCh1cmwsIGRhdGEsIGNvbmZpZyksXHJcblxyXG4gICAgZGVsZXRlOiAodXJsLCBjb25maWcgPSB7fSkgPT4gYXhpb3NJbnN0YW5jZS5kZWxldGUodXJsLCBjb25maWcpLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgYXBpU2VydmljZTtcclxuIl0sIm5hbWVzIjpbImF4aW9zSW5zdGFuY2UiLCJhcGlTZXJ2aWNlIiwiZ2V0IiwidXJsIiwicGFyYW1zIiwicG9zdCIsImRhdGEiLCJjb25maWciLCJwdXQiLCJkZWxldGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/apiService.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/cartService.js":
/*!*****************************************!*\
  !*** ./src/app/services/cartService.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/apiService */ \"(app-pages-browser)/./src/app/lib/apiService.js\");\n\nconst cartService = {\n    // Get the user's cart\n    getCart: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/cart/get-cart\", {\n            withCredentials: true\n        }),\n    // Add an item to the cart\n    addItemToCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/add-item\", data, {\n            withCredentials: true\n        }),\n    // Remove an item from the cart\n    removeItemFromCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/remove-item\", data, {\n            withCredentials: true\n        }),\n    // Clear the user's cart\n    clearCart: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/clear\", {\n            withCredentials: true\n        }),\n    // Update item quantity in the cart\n    updateCartItemQuantity: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/cart/update-item\", data, {\n            withCredentials: true\n        }),\n    // Update item period in the cart\n    updateItemPeriod: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/cart/update-item-period\", data, {\n            withCredentials: true\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (cartService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/cartService.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DynamicMetadataClient.jsx":
/*!**************************************************!*\
  !*** ./src/components/DynamicMetadataClient.jsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst DynamicMetadataClient = (param)=>{\n    let { title, desc } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\DynamicMetadataClient.jsx\",\n                lineNumber: 6,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: desc\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\DynamicMetadataClient.jsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c = DynamicMetadataClient;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DynamicMetadataClient);\nvar _c;\n$RefreshReg$(_c, \"DynamicMetadataClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0R5bmFtaWNNZXRhZGF0YUNsaWVudC5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUI7QUFFekIsTUFBTUMsd0JBQXdCO1FBQUMsRUFBQ0MsS0FBSyxFQUFFQyxJQUFJLEVBQUM7SUFDMUMscUJBQ0U7OzBCQUNJLDhEQUFDRDswQkFBT0E7Ozs7OzswQkFDUiw4REFBQ0U7Z0JBQUtDLE1BQUs7Z0JBQWNDLFNBQVNIOzs7Ozs7OztBQUcxQztLQVBNRjtBQVNOLCtEQUFlQSxxQkFBcUJBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvRHluYW1pY01ldGFkYXRhQ2xpZW50LmpzeD81NWUyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcclxuXHJcbmNvbnN0IER5bmFtaWNNZXRhZGF0YUNsaWVudCA9ICh7dGl0bGUsIGRlc2N9KSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgICAgPHRpdGxlPnt0aXRsZX08L3RpdGxlPlxyXG4gICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9e2Rlc2N9IC8+XHJcbiAgICA8Lz5cclxuICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IER5bmFtaWNNZXRhZGF0YUNsaWVudCJdLCJuYW1lcyI6WyJSZWFjdCIsIkR5bmFtaWNNZXRhZGF0YUNsaWVudCIsInRpdGxlIiwiZGVzYyIsIm1ldGEiLCJuYW1lIiwiY29udGVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DynamicMetadataClient.jsx\n"));

/***/ })

});