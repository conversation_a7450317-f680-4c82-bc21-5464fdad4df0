"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_shared_faq2_jsx"],{

/***/ "(app-pages-browser)/./src/components/shared/faq2.jsx":
/*!****************************************!*\
  !*** ./src/components/shared/faq2.jsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDown,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst FAQ2 = (param)=>{\n    let { t } = param;\n    var _faqItems_activeIndex, _faqItems_activeIndex1, _faqItems_;\n    _s();\n    const [activeIndex, setActiveIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isAutoCycling, setIsAutoCycling] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check if we're on mobile or desktop\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkIfMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n            // Set initial auto-cycling state based on device type\n            setIsAutoCycling(window.innerWidth >= 768);\n        };\n        // Check on initial load\n        checkIfMobile();\n        // Listen for resize events\n        window.addEventListener(\"resize\", checkIfMobile);\n        return ()=>{\n            window.removeEventListener(\"resize\", checkIfMobile);\n        };\n    }, []);\n    const faqItems = [\n        {\n            question: t(\"faq_1_question\"),\n            answer: t(\"faq_1_answer\")\n        },\n        {\n            question: t(\"faq_2_question\"),\n            answer: t(\"faq_2_answer\")\n        },\n        {\n            question: t(\"faq_3_question\"),\n            answer: t(\"faq_3_answer\")\n        },\n        {\n            question: t(\"faq_4_question\"),\n            answer: t(\"faq_4_answer\")\n        }\n    ];\n    const cycleToNextFaq = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setActiveIndex((prevIndex)=>{\n            const nextIndex = prevIndex === null ? 0 : (prevIndex + 1) % faqItems.length;\n            return nextIndex;\n        });\n    }, [\n        faqItems.length\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let intervalId;\n        if (isAutoCycling) {\n            intervalId = setInterval(cycleToNextFaq, 10000); // Change every 10 seconds\n        }\n        return ()=>{\n            if (intervalId) clearInterval(intervalId);\n        };\n    }, [\n        isAutoCycling,\n        cycleToNextFaq\n    ]);\n    const toggleAccordion = (index)=>{\n        // For both mobile and desktop: set active index\n        setActiveIndex(index === activeIndex ? null : index);\n        // But only enable auto-cycling on desktop\n        if (!isMobile) {\n            setIsAutoCycling(true);\n        } else {\n            setIsAutoCycling(false);\n        }\n    };\n    // Helper function to render answer content with proper formatting\n    const renderAnswerContent = (answer)=>{\n        // First normalize the newlines by replacing single \\n with \\n\\n if they aren't already\n        const normalizedAnswer = answer.replace(RegExp(\"(?<!\\\\n)\\\\n(?!\\\\n)\", \"g\"), \"\\n\\n\");\n        return normalizedAnswer.split(\"\\n\\n\").map((paragraph, idx)=>{\n            // Check if paragraph contains bullet points\n            if (paragraph.includes(\"- \")) {\n                const [intro, ...bulletPoints] = paragraph.split(\"- \");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: [\n                        intro && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-2\",\n                            children: intro\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                            lineNumber: 92,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"list-disc pl-5 space-y-1\",\n                            children: bulletPoints.map((point, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: point.trim()\n                                }, i, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, idx, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, undefined);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-4\",\n                children: paragraph\n            }, idx, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined);\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"w-full py-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                        variant: \"h1\",\n                        className: \"font-bold text-3xl mb-4\",\n                        children: t(\"faq_title\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hidden md:flex flex-row items-center justify-center gap-4 px-10\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-[70%] -mr-14 h-auto rounded-lg overflow-hidden shadow-lg z-10\",\n                            children: faqItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 cursor-pointer transition-all duration-300 flex items-center gap-4 \".concat(activeIndex === index ? \"bg-[#E0E3FF] shadow-sm\" : \"bg-white hover:bg-gray-100\"),\n                                    onClick: ()=>toggleAccordion(index),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-5 h-5 \".concat(activeIndex === index ? \"bg-[#606AF5]\" : \"bg-[#606AF566]\", \" rounded-full\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-[90%] flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-medium text-sm pr-4\",\n                                                    children: item.question\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-500 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, \"desktop-\".concat(index), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full bg-[#FAFBFF] h-[630px] py-6 px-14 rounded-lg shadow-md overflow-y-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-semibold text-xl mb-4\",\n                                    children: activeIndex !== null ? (_faqItems_activeIndex = faqItems[activeIndex]) === null || _faqItems_activeIndex === void 0 ? void 0 : _faqItems_activeIndex.question : \"Hosting with ZtechEngineering\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-700 text-sm leading-relaxed\",\n                                    children: activeIndex !== null ? renderAnswerContent((_faqItems_activeIndex1 = faqItems[activeIndex]) === null || _faqItems_activeIndex1 === void 0 ? void 0 : _faqItems_activeIndex1.answer) : renderAnswerContent((_faqItems_ = faqItems[0]) === null || _faqItems_ === void 0 ? void 0 : _faqItems_.answer)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"md:hidden max-w-lg mx-auto px-4\",\n                    children: faqItems.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 border rounded-lg overflow-hidden shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 cursor-pointer flex items-center justify-between \".concat(activeIndex === index ? \"bg-[#E0E3FF]\" : \"bg-white\"),\n                                    onClick: ()=>toggleAccordion(index),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-medium text-sm pr-2\",\n                                            children: item.question\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 mr-2 rounded-full \".concat(activeIndex === index ? \"bg-[#606AF5]\" : \"bg-[#606AF566]\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDown_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-500 transition-transform duration-300 \".concat(activeIndex === index ? \"rotate-180\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-hidden transition-all duration-300 bg-[#FAFBFF] \".concat(activeIndex === index ? \"max-h-[2000px] p-4\" : \"max-h-0\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-700 text-sm leading-relaxed\",\n                                        children: renderAnswerContent(item.answer)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, \"mobile-\".concat(index), true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                            lineNumber: 167,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\shared\\\\faq2.jsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, undefined);\n};\n_s(FAQ2, \"52JbX9Eg+NguzirkNPJ4eMYlgw4=\");\n_c = FAQ2;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FAQ2);\nvar _c;\n$RefreshReg$(_c, \"FAQ2\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/shared/faq2.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-down.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ ChevronDown; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m6 9 6 6 6-6\",\n            key: \"qrunsl\"\n        }\n    ]\n];\nconst ChevronDown = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronDown\", __iconNode);\n //# sourceMappingURL=chevron-down.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1kb3duLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBOzs7OztDQUtDLEdBRXFEO0FBRXRELE1BQU1DLGFBQWE7SUFBQztRQUFDO1FBQVE7WUFBRUMsR0FBRztZQUFnQkMsS0FBSztRQUFTO0tBQUU7Q0FBQztBQUNuRSxNQUFNQyxjQUFjSixnRUFBZ0JBLENBQUMsZUFBZUM7QUFFTixDQUM5Qyx3Q0FBd0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9jaGV2cm9uLWRvd24uanM/ZTU3ZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC40NzUuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IF9faWNvbk5vZGUgPSBbW1wicGF0aFwiLCB7IGQ6IFwibTYgOSA2IDYgNi02XCIsIGtleTogXCJxcnVuc2xcIiB9XV07XG5jb25zdCBDaGV2cm9uRG93biA9IGNyZWF0ZUx1Y2lkZUljb24oXCJDaGV2cm9uRG93blwiLCBfX2ljb25Ob2RlKTtcblxuZXhwb3J0IHsgX19pY29uTm9kZSwgQ2hldnJvbkRvd24gYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y2hldnJvbi1kb3duLmpzLm1hcFxuIl0sIm5hbWVzIjpbImNyZWF0ZUx1Y2lkZUljb24iLCJfX2ljb25Ob2RlIiwiZCIsImtleSIsIkNoZXZyb25Eb3duIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js":
/*!*******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/chevron-right.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ ChevronRight; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"m9 18 6-6-6-6\",\n            key: \"mthhwq\"\n        }\n    ]\n];\nconst ChevronRight = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronRight\", __iconNode);\n //# sourceMappingURL=chevron-right.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hldnJvbi1yaWdodC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7Ozs7Q0FLQyxHQUVxRDtBQUV0RCxNQUFNQyxhQUFhO0lBQUM7UUFBQztRQUFRO1lBQUVDLEdBQUc7WUFBaUJDLEtBQUs7UUFBUztLQUFFO0NBQUM7QUFDcEUsTUFBTUMsZUFBZUosZ0VBQWdCQSxDQUFDLGdCQUFnQkM7QUFFUCxDQUMvQyx5Q0FBeUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9jaGV2cm9uLXJpZ2h0LmpzPzY3YzgiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuNDc1LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBfX2ljb25Ob2RlID0gW1tcInBhdGhcIiwgeyBkOiBcIm05IDE4IDYtNi02LTZcIiwga2V5OiBcIm10aGh3cVwiIH1dXTtcbmNvbnN0IENoZXZyb25SaWdodCA9IGNyZWF0ZUx1Y2lkZUljb24oXCJDaGV2cm9uUmlnaHRcIiwgX19pY29uTm9kZSk7XG5cbmV4cG9ydCB7IF9faWNvbk5vZGUsIENoZXZyb25SaWdodCBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jaGV2cm9uLXJpZ2h0LmpzLm1hcFxuIl0sIm5hbWVzIjpbImNyZWF0ZUx1Y2lkZUljb24iLCJfX2ljb25Ob2RlIiwiZCIsImtleSIsIkNoZXZyb25SaWdodCIsImRlZmF1bHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\n"));

/***/ })

}]);