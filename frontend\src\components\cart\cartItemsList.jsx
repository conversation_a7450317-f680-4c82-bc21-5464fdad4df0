"use client";
import React from "react";
import { Typography } from "@material-tailwind/react";
import CartItem from "./cartItem";
import { ShoppingCart } from "lucide-react";

function CartItemsList({ cartItems, onQuantityChange, onPeriodChange, t }) {
  if (cartItems.length === 0) {
    return (
      <div className="mx-auto px-4">
        <div className="text-center">
          <ShoppingCart className="h-14 w-14 text-gray-500 mx-auto mb-4" />
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            {t("cart_empty")}
          </h2>
          <p className="text-gray-600 mb-8">{t("cart_empty_message")}</p>
        </div>
      </div>
    );
  }
  return (
    <div>
      <Typography
        variant="h2"
        className="text-lg font-medium text-gray-900 mb-4"
      >
        {t("cart_items")}
      </Typography>
      <div className="">
        {cartItems.map((item) => (
          <CartItem
            key={item._id}
            item={item}
            onQuantityChange={onQuantityChange}
            onPeriodChange={onPeriodChange}
            t={t}
          />
        ))}
      </div>
    </div>
  );
}

export default CartItemsList;
