"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(sharedPages)/auth/login/page",{

/***/ "(app-pages-browser)/./src/app/config/constant.js":
/*!************************************!*\
  !*** ./src/app/config/constant.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_GOOGLE_MAP_KEY: function() { return /* binding */ API_GOOGLE_MAP_KEY; },\n/* harmony export */   BACKEND_URL: function() { return /* binding */ BACKEND_URL; },\n/* harmony export */   COOKIE_DOMAIN: function() { return /* binding */ COOKIE_DOMAIN; },\n/* harmony export */   FRONTEND_URL: function() { return /* binding */ FRONTEND_URL; },\n/* harmony export */   REACT_APP_GG_APP_ID: function() { return /* binding */ REACT_APP_GG_APP_ID; },\n/* harmony export */   isProd: function() { return /* binding */ isProd; }\n/* harmony export */ });\nconst backendDev = \"http://localhost:5002\";\nconst frontendDev = \"http://localhost:3001\";\nconst backend = \"https://api.ztechengineering.com\";\nconst frontend = \"https://ztechengineering.com\";\nconst isProd = true;\nconst BACKEND_URL = isProd ? backend : backendDev;\nconst FRONTEND_URL = isProd ? frontend : frontendDev;\nconst COOKIE_DOMAIN = isProd ? \".ztechengineering.com\" : \"localhost\";\nconst REACT_APP_GG_APP_ID = \"480987384459-h3cie2vcshp09vphuvnshccqprco3fbo.apps.googleusercontent.com\";\nconst API_GOOGLE_MAP_KEY = \"AIzaSyA5pGy3UEKwbgjUY-72RmoR7npEq1b_uf0\";\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29uZmlnL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFBLE1BQU1BLGFBQWE7QUFDbkIsTUFBTUMsY0FBYztBQUVwQixNQUFNQyxVQUFVO0FBQ2hCLE1BQU1DLFdBQVc7QUFFVixNQUFNQyxTQUFTLEtBQUs7QUFDcEIsTUFBTUMsY0FBY0QsU0FBU0YsVUFBVUYsV0FBVztBQUNsRCxNQUFNTSxlQUFlRixTQUFTRCxXQUFXRixZQUFZO0FBQ3JELE1BQU1NLGdCQUFnQkgsU0FBUywwQkFBMEIsWUFBWTtBQUVyRSxNQUFNSSxzQkFDWCwyRUFBMkU7QUFDdEUsTUFBTUMscUJBQXFCLDBDQUEwQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2NvbmZpZy9jb25zdGFudC5qcz9iMTZjIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhY2tlbmREZXYgPSBcImh0dHA6Ly9sb2NhbGhvc3Q6NTAwMlwiO1xyXG5jb25zdCBmcm9udGVuZERldiA9IFwiaHR0cDovL2xvY2FsaG9zdDozMDAxXCI7XHJcblxyXG5jb25zdCBiYWNrZW5kID0gXCJodHRwczovL2FwaS56dGVjaGVuZ2luZWVyaW5nLmNvbVwiO1xyXG5jb25zdCBmcm9udGVuZCA9IFwiaHR0cHM6Ly96dGVjaGVuZ2luZWVyaW5nLmNvbVwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IGlzUHJvZCA9IHRydWU7XHJcbmV4cG9ydCBjb25zdCBCQUNLRU5EX1VSTCA9IGlzUHJvZCA/IGJhY2tlbmQgOiBiYWNrZW5kRGV2O1xyXG5leHBvcnQgY29uc3QgRlJPTlRFTkRfVVJMID0gaXNQcm9kID8gZnJvbnRlbmQgOiBmcm9udGVuZERldjtcclxuZXhwb3J0IGNvbnN0IENPT0tJRV9ET01BSU4gPSBpc1Byb2QgPyBcIi56dGVjaGVuZ2luZWVyaW5nLmNvbVwiIDogXCJsb2NhbGhvc3RcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBSRUFDVF9BUFBfR0dfQVBQX0lEID1cclxuICBcIjQ4MDk4NzM4NDQ1OS1oM2NpZTJ2Y3NocDA5dnBodXZuc2hjY3FwcmNvM2Ziby5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbVwiO1xyXG5leHBvcnQgY29uc3QgQVBJX0dPT0dMRV9NQVBfS0VZID0gXCJBSXphU3lBNXBHeTNVRUt3YmdqVVktNzJSbW9SN25wRXExYl91ZjBcIjtcclxuIl0sIm5hbWVzIjpbImJhY2tlbmREZXYiLCJmcm9udGVuZERldiIsImJhY2tlbmQiLCJmcm9udGVuZCIsImlzUHJvZCIsIkJBQ0tFTkRfVVJMIiwiRlJPTlRFTkRfVVJMIiwiQ09PS0lFX0RPTUFJTiIsIlJFQUNUX0FQUF9HR19BUFBfSUQiLCJBUElfR09PR0xFX01BUF9LRVkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/config/constant.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/context/AuthContext.jsx":
/*!*****************************************!*\
  !*** ./src/app/context/AuthContext.jsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/authService */ \"(app-pages-browser)/./src/app/services/authService.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Create the Auth Context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Create a Provider Component\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cartCount, setCartCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if user exists in localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            await checkAuth();\n            setLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    const checkAuth = async ()=>{\n        setLoading(true);\n        try {\n            const response = await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].checkAuth();\n            const updatedUser = response.data.user;\n            console.log(\"Fetched user:\", updatedUser);\n            // Update only if the data is different\n            if (JSON.stringify(updatedUser) !== localStorage.getItem(\"user\")) {\n                localStorage.setItem(\"user\", JSON.stringify(updatedUser));\n                document.cookie = \"role=\".concat(updatedUser.role, \"; max-age=604800; path=/; secure\");\n            }\n            setUser(updatedUser);\n            return updatedUser;\n        } catch (err) {\n            console.error(\"Auth check failed:\", err);\n            setUser(null);\n            localStorage.removeItem(\"user\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Handle authentication error\n    const handleAuthError = (error)=>{\n        var _error_response_data, _error_response;\n        const message = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"An unexpected error occurred\";\n        console.error(error);\n        return error;\n    };\n    // Login function\n    const login = async (credentials)=>{\n        setLoading(true);\n        try {\n            const loginRes = await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].login(credentials);\n            const userData = loginRes.data.user;\n            setUser(userData);\n            // Store user in localStorage\n            localStorage.setItem(\"user\", JSON.stringify(userData));\n            document.cookie = \"role=\".concat(userData.role, \"; max-age=604800; path=/; secure\");\n            // Check for pending cart items\n            const pendingItemJson = localStorage.getItem(\"pendingCartItem\");\n            if (pendingItemJson) {\n                try {\n                    // We'll handle this in a separate function after login completes\n                    // Just mark that we have a pending item\n                    loginRes.data.hasPendingCartItem = true;\n                } catch (cartError) {\n                    console.error(\"Error handling pending cart item:\", cartError);\n                }\n            }\n            return loginRes;\n        } catch (error) {\n            var _error_response, _error_response1;\n            const detailedError = {\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                data: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data\n            };\n            throw detailedError;\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Logout function\n    const logout = async ()=>{\n        setLoading(true);\n        try {\n            await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].logout();\n            // Clear user from localStorage\n            localStorage.removeItem(\"user\");\n            // Clear cookies on logout\n            document.cookie = \"role=; Max-Age=0; path:/\";\n            document.cookie = \"refresh_token=; Max-Age=0; path=/;\";\n            document.cookie = \"token=; Max-Age=0; path=/;\";\n            setUser(null);\n            router.refresh();\n            router.push(\"/auth/login\"); // Redirect to login page after logout\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.log(\"Logout error:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Compute if user is authenticated\n    const isAuthenticated = !!user;\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            user,\n            loading,\n            login,\n            logout,\n            checkAuth,\n            cartCount,\n            setCartCount,\n            isAuthenticated\n        }), [\n        user,\n        loading,\n        cartCount,\n        checkAuth,\n        isAuthenticated\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\context\\\\AuthContext.jsx\",\n        lineNumber: 143,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AuthProvider, \"vGpTtn+k/cSLMBUJ8G2NeTQqYd0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\n// Custom hook for using AuthContext\nconst useAuth = ()=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n};\n_s1(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/context/AuthContext.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/lib/apiService.js":
/*!***********************************!*\
  !*** ./src/app/lib/apiService.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _axiosInstance__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./axiosInstance */ \"(app-pages-browser)/./src/app/lib/axiosInstance.js\");\n\nconst apiService = {\n    get: function(url) {\n        let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n            params\n        });\n    },\n    post: function(url) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, data, config);\n    },\n    put: function(url) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, data, config);\n    },\n    delete: function(url) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(url, config);\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbGliL2FwaVNlcnZpY2UuanMiLCJtYXBwaW5ncyI6Ijs7QUFBNEM7QUFFNUMsTUFBTUMsYUFBYTtJQUNmQyxLQUFLLFNBQUNDO1lBQUtDLDBFQUFTLENBQUM7ZUFBTUosc0RBQWFBLENBQUNFLEdBQUcsQ0FBQ0MsS0FBSztZQUFFQztRQUFPOztJQUUzREMsTUFBTSxTQUFDRjtZQUFLRyx3RUFBTyxDQUFDLEdBQUdDLDBFQUFTLENBQUM7ZUFBTVAsc0RBQWFBLENBQUNLLElBQUksQ0FBQ0YsS0FBS0csTUFBTUM7O0lBRXJFQyxLQUFLLFNBQUNMO1lBQUtHLHdFQUFPLENBQUMsR0FBR0MsMEVBQVMsQ0FBQztlQUFNUCxzREFBYUEsQ0FBQ1EsR0FBRyxDQUFDTCxLQUFLRyxNQUFNQzs7SUFFbkVFLFFBQVEsU0FBQ047WUFBS0ksMEVBQVMsQ0FBQztlQUFNUCxzREFBYUEsQ0FBQ1MsTUFBTSxDQUFDTixLQUFLSTs7QUFDNUQ7QUFFQSwrREFBZU4sVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2xpYi9hcGlTZXJ2aWNlLmpzPzYxZDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zSW5zdGFuY2UgZnJvbSAnLi9heGlvc0luc3RhbmNlJztcclxuXHJcbmNvbnN0IGFwaVNlcnZpY2UgPSB7XHJcbiAgICBnZXQ6ICh1cmwsIHBhcmFtcyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLmdldCh1cmwsIHsgcGFyYW1zIH0pLFxyXG5cclxuICAgIHBvc3Q6ICh1cmwsIGRhdGEgPSB7fSwgY29uZmlnID0ge30pID0+IGF4aW9zSW5zdGFuY2UucG9zdCh1cmwsIGRhdGEsIGNvbmZpZyksXHJcblxyXG4gICAgcHV0OiAodXJsLCBkYXRhID0ge30sIGNvbmZpZyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLnB1dCh1cmwsIGRhdGEsIGNvbmZpZyksXHJcblxyXG4gICAgZGVsZXRlOiAodXJsLCBjb25maWcgPSB7fSkgPT4gYXhpb3NJbnN0YW5jZS5kZWxldGUodXJsLCBjb25maWcpLFxyXG59O1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgYXBpU2VydmljZTtcclxuIl0sIm5hbWVzIjpbImF4aW9zSW5zdGFuY2UiLCJhcGlTZXJ2aWNlIiwiZ2V0IiwidXJsIiwicGFyYW1zIiwicG9zdCIsImRhdGEiLCJjb25maWciLCJwdXQiLCJkZWxldGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/apiService.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/cartService.js":
/*!*****************************************!*\
  !*** ./src/app/services/cartService.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/apiService */ \"(app-pages-browser)/./src/app/lib/apiService.js\");\n\nconst cartService = {\n    // Get the user's cart\n    getCart: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/cart/get-cart\", {\n            withCredentials: true\n        }),\n    // Add an item to the cart\n    addItemToCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/add-item\", data, {\n            withCredentials: true\n        }),\n    // Remove an item from the cart\n    removeItemFromCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/remove-item\", data, {\n            withCredentials: true\n        }),\n    // Clear the user's cart\n    clearCart: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/clear\", {\n            withCredentials: true\n        }),\n    // Update item quantity in the cart\n    updateCartItemQuantity: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/cart/update-item\", data, {\n            withCredentials: true\n        }),\n    // Update item period in the cart\n    updateItemPeriod: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/cart/update-item-period\", data, {\n            withCredentials: true\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (cartService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/cartService.js\n"));

/***/ })

});